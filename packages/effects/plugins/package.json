{"name": "@vben/plugins", "version": "5.5.8", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/effects/plugins"}, "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "exports": {"./echarts": {"types": "./src/echarts/index.ts", "default": "./src/echarts/index.ts"}, "./vxe-table": {"types": "./src/vxe-table/index.ts", "default": "./src/vxe-table/index.ts"}, "./motion": {"types": "./src/motion/index.ts", "default": "./src/motion/index.ts"}}, "dependencies": {"@vben-core/form-ui": "workspace:*", "@vben-core/shadcn-ui": "workspace:*", "@vben-core/shared": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/locales": "workspace:*", "@vben/preferences": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "@vueuse/motion": "catalog:", "echarts": "catalog:", "vue": "catalog:", "vxe-pc-ui": "catalog:", "vxe-table": "catalog:"}}